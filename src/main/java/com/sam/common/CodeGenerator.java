package com.sam.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-15 11:30
 */
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.Collections;
import java.util.Scanner;

public class CodeGenerator {

    /**
     * 控制台输入
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        System.out.println("请输入" + tip + "：");
        if (scanner.hasNext()) {
            String input = scanner.next();
            if (input != null && input.trim().length() > 0) {
                return input.trim();
            }
        }
        throw new RuntimeException("请输入正确的 " + tip + "！");
    }

    public static void main(String[] args) {
        // 1. 数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig.Builder(
                "**********************************************************************************************************************************",
                "root",
                "00000000"
        ).driverClassName("com.mysql.cj.jdbc.Driver").build();

        // 2. 全局配置
        GlobalConfig globalConfig = new GlobalConfig.Builder()
                .outputDir(System.getProperty("user.dir") + "/src/main/java") // 输出目录
                .author("Sam")
                .enableSwagger()       // 开启 swagger2
                .disableOpenDir()      // 生成完不打开资源管理器
                .commentDate("yyyy-MM-dd HH:mm:ss")
                .dateType(DateType.TIME_PACK) // 使用java.time包
                .build();

        // 3. 包配置（VMBS项目结构）
        PackageConfig packageConfig = new PackageConfig.Builder()
                .parent("com.sam")     // 父包名
                .entity("entity")      // entity 包
                .service("service")    // service 包
                .serviceImpl("service.impl")
                .mapper("mapper")
                .controller("controller")
                .pathInfo(Collections.singletonMap(OutputFile.xml, System.getProperty("user.dir") + "/src/main/resources/mapper"))
                .build();

        // 4. 策略配置
        StrategyConfig strategyConfig = new StrategyConfig.Builder()
                .addInclude(scanner("表名，多个英文逗号分割").split(",")) // 设置要生成的表
                .addTablePrefix("vm_", "t_", "sys_") // 去掉表前缀（VMBS项目可能使用vm_前缀）
                .entityBuilder()
                .enableLombok()
                .enableTableFieldAnnotation() // 启用字段注解
                .controllerBuilder()
                .enableRestStyle() // @RestController
                .enableHyphenStyle() // url驼峰转连字符
                .mapperBuilder()
                .enableBaseResultMap()
                .enableBaseColumnList()
                .serviceBuilder()
                .formatServiceFileName("%sService") // Service接口命名格式
                .formatServiceImplFileName("%sServiceImpl") // ServiceImpl实现类命名格式
                .build();

        // 5. 模板配置
        TemplateConfig templateConfig = new TemplateConfig.Builder().build();

        // 6. 代码生成器
        AutoGenerator generator = new AutoGenerator(dataSourceConfig);
        generator.global(globalConfig);
        generator.packageInfo(packageConfig);
        generator.strategy(strategyConfig);
        generator.template(templateConfig);

        // 7. 执行生成
        generator.execute(new FreemarkerTemplateEngine());

        System.out.println("代码生成完毕！");
    }
}