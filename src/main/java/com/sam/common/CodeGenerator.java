package com.sam.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-15 11:30
 */
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.Collections;
import java.util.Scanner;

public class CodeGenerator {

    /**
     * 控制台输入
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        System.out.println("请输入" + tip + "：");
        if (scanner.hasNext()) {
            String input = scanner.next();
            if (input != null && input.trim().length() > 0) {
                return input.trim();
            }
        }
        throw new RuntimeException("请输入正确的 " + tip + "！");
    }

    public static void main(String[] args) {
        // 1. 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 2. 全局配置
        GlobalConfig gc = new GlobalConfig.Builder()
                .outputDir(System.getProperty("user.dir") + "/src/main/java") // 输出目录
                .author("Sam")
                .enableSwagger()       // 开启 swagger2
                .disableOpenDir()      // 生成完不打开资源管理器
                .commentDate("yyyy-MM-dd")
                .build();
        mpg.global(gc);

        // 3. 数据源配置
        DataSourceConfig dsc = new DataSourceConfig
                .Builder("******************************************************************************************************************************************",
                "root", "00000000")
                .driver("com.mysql.cj.jdbc.Driver")
                .build();
        mpg.dataSource(dsc);

        // 4. 包配置（传统模式，不分模块）
        PackageConfig pc = new PackageConfig.Builder()
                .parent("proj.book")    // 父包名
                .entity("entity")      // entity 包
                .service("service")    // service 包
                .serviceImpl("service.impl")
                .mapper("mapper")
                .controller("controller")
                .build();
        mpg.packageInfo(pc);

        // 5. 自定义输出配置（mapper.xml 文件放到 resources/mapper 下）
        mpg.injection(new InjectionConfig.Builder()
                .customFile(Collections.emptyMap()) // 不自定义额外文件
                .build()
        );

        mpg.template(new TemplateConfig.Builder()
                .entity("/templates/entity.java") // 默认即可
                .service("/templates/service.java")
                .serviceImpl("/templates/serviceImpl.java")
                .mapper("/templates/mapper.java")
                .xml("/templates/mapper.xml")
                .controller("/templates/controller.java")
                .build()
        );

        mpg.pathInfo(Collections.singletonMap(
                OutputFile.xml, System.getProperty("user.dir") + "/src/main/resources/mapper"
        ));

        // 6. 策略配置
        StrategyConfig strategy = new StrategyConfig.Builder()
                .addInclude(scanner("表名，多个英文逗号分割").split(",")) // 设置要生成的表
                .addTablePrefix("t_", "sys_") // 去掉表前缀（可选）
                .entityBuilder()
                .enableLombok()
                .controllerBuilder()
                .enableRestStyle() // @RestController
                .enableHyphenStyle() // url驼峰转连字符
                .mapperBuilder()
                .enableBaseResultMap()
                .enableBaseColumnList()
                .build();
        mpg.strategy(strategy);

        // 7. 模板引擎
        mpg.execute(new FreemarkerTemplateEngine());

        System.out.println("代码生成完毕！");
    }
}