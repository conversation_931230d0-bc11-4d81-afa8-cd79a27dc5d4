server:
  # 端口号设置
  port: 8080
  #项目根路径
  servlet:
    context-path: /

# 配置mybatis
mybatis:
  configuration:
    #<settings> 如何自动映射列和属性 none表示取消自动映射 full会自动映射(不论是否嵌套) 默认 partial 只会映射没有嵌套结果的结果集
    auto-mapping-behavior: full
    #驼峰命名 user_id  userId
    map-underscore-to-camel-case: true
    #开启延迟加载
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    #日志
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  # 配置实体类别名
  type-aliases-package: com.sam.entity
  # mapper映射文件位置
  mapper-locations: classpath:/mapper/*.xml

spring:
  mvc:
    static-path-pattern: /static/**  # 静态资源访问路径模式
  web:
    resources:
      static-locations: classpath:/static/,classpath:/public/  # 静态资源位置
  servlet:
    multipart:
      enabled: true                    # 启用文件上传
      max-file-size: 10MB              # 单个文件最大大小
      max-request-size: 100MB           # 整个请求最大大小

  datasource:
    # 连接池类型
    type: com.alibaba.druid.pool.DruidDataSource
    # Druid的其他属性配置 springboot3整合情况下,数据库连接信息必须在Druid属性下!
    druid:
      db-type: mysql
      url: **********************************************************************************************************************************
      username: root
      password: '00000000'
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
